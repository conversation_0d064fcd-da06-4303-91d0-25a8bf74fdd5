<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PageController extends Controller
{
    /**
     * Display a listing of active pages.
     */
    public function index(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $pages = Page::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($page) use ($locale) {
                return [
                    'id' => $page->id,
                    'slug' => $page->slug,
                    'title' => $page->getTranslation('title', $locale),
                    'meta_description' => $page->getTranslation('meta_description', $locale),
                    'meta_keywords' => $page->getTranslation('meta_keywords', $locale),
                    'sort_order' => $page->sort_order,
                    'sections_count' => $page->sections()->where('is_active', true)->count(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $pages,
            'locale' => $locale,
        ]);
    }

    /**
     * Display the specified page with its sections.
     */
    public function show(Request $request, string $slug): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $page = Page::where('slug', $slug)
            ->where('is_active', true)
            ->with(['sections' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }])
            ->first();

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found',
            ], 404);
        }

        $pageData = [
            'id' => $page->id,
            'slug' => $page->slug,
            'title' => $page->getTranslation('title', $locale),
            'meta_description' => $page->getTranslation('meta_description', $locale),
            'meta_keywords' => $page->getTranslation('meta_keywords', $locale),
            'sort_order' => $page->sort_order,
            'sections' => $page->sections->map(function ($section) use ($locale) {
                return [
                    'id' => $section->id,
                    'type' => $section->type,
                    'title' => $section->getTranslation('title', $locale),
                    'subtitle' => $section->getTranslation('subtitle', $locale),
                    'content' => $section->getTranslation('content', $locale),
                    'settings' => $section->settings,
                    'sort_order' => $section->sort_order,
                    'media' => $section->getMedia('images')->map(function ($media) {
                        return [
                            'id' => $media->id,
                            'url' => $media->getUrl(),
                            'thumb_url' => $media->getUrl('thumb'),
                            'name' => $media->name,
                            'alt' => $media->getCustomProperty('alt', ''),
                        ];
                    }),
                ];
            }),
        ];

        return response()->json([
            'success' => true,
            'data' => $pageData,
            'locale' => $locale,
        ]);
    }
}
