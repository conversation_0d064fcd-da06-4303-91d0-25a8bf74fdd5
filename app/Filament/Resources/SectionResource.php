<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SectionResource\Pages;
use App\Models\Section;
use App\Models\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class SectionResource extends Resource
{
    protected static ?string $model = Section::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?string $navigationGroup = 'Content Management';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Section Configuration')
                    ->schema([
                        Forms\Components\Select::make('page_id')
                            ->relationship('page', 'slug')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('type')
                            ->required()
                            ->options([
                                'hero' => 'Hero Section',
                                'results' => 'Results Showcase',
                                'brands' => 'Trusted Brands',
                                'testimonials' => 'Testimonials',
                                'industries' => 'Cross-Industry',
                                'services' => 'Why CRO Works',
                                'cta' => 'Call to Action',
                                'footer' => 'Footer',
                                'custom' => 'Custom Section',
                            ])
                            ->native(false),

                        Forms\Components\Toggle::make('is_active')
                            ->default(true),

                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0)
                            ->helperText('Order in which sections appear (lower numbers first)'),
                    ])->columns(2),

                Forms\Components\Section::make('Content (English)')
                    ->schema([
                        Forms\Components\TextInput::make('title.en')
                            ->label('Title (English)')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('subtitle.en')
                            ->label('Subtitle (English)')
                            ->maxLength(255),

                        Forms\Components\RichEditor::make('content.en')
                            ->label('Content (English)')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Content (Arabic)')
                    ->schema([
                        Forms\Components\TextInput::make('title.ar')
                            ->label('Title (Arabic)')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('subtitle.ar')
                            ->label('Subtitle (Arabic)')
                            ->maxLength(255),

                        Forms\Components\RichEditor::make('content.ar')
                            ->label('Content (Arabic)')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Media & Settings')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('images')
                            ->collection('images')
                            ->multiple()
                            ->reorderable()
                            ->columnSpanFull(),

                        Forms\Components\KeyValue::make('settings')
                            ->label('Additional Settings')
                            ->keyLabel('Setting Key')
                            ->valueLabel('Setting Value')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('page.slug')
                    ->label('Page')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'hero' => 'success',
                        'results' => 'info',
                        'brands' => 'warning',
                        'testimonials' => 'primary',
                        'industries' => 'secondary',
                        'services' => 'gray',
                        'cta' => 'danger',
                        'footer' => 'slate',
                        default => 'gray',
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('title')
                    ->getStateUsing(fn ($record) => $record->getTranslation('title', app()->getLocale()))
                    ->searchable()
                    ->limit(50),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('page_id')
                    ->relationship('page', 'slug')
                    ->label('Page'),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'hero' => 'Hero Section',
                        'results' => 'Results Showcase',
                        'brands' => 'Trusted Brands',
                        'testimonials' => 'Testimonials',
                        'industries' => 'Cross-Industry',
                        'services' => 'Why CRO Works',
                        'cta' => 'Call to Action',
                        'footer' => 'Footer',
                        'custom' => 'Custom Section',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active sections only')
                    ->falseLabel('Inactive sections only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->reorderable('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSections::route('/'),
            'create' => Pages\CreateSection::route('/create'),
            'edit' => Pages\EditSection::route('/{record}/edit'),
        ];
    }
}
