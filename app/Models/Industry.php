<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Industry extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'icon_class',
        'description',
        'is_active',
        'sort_order'
    ];

    protected $translatable = [
        'name',
        'description'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];
}
