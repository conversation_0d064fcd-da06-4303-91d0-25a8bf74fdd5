@extends('layouts.spa')

@section('content')
<!-- Hero Section -->
<section id="hero" class="relative min-h-screen flex items-center justify-center overflow-hidden bg-black">
    <!-- Background Video -->
    <div class="absolute inset-0 w-full h-full">
        <video autoplay muted loop class="w-full h-full object-cover opacity-40">
            <source src="https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1c9a91a20&profile_id=139&oauth2_token_id=57447761" type="video/mp4">
        </video>
        <div class="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70"></div>
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 text-center px-6 max-w-6xl mx-auto" x-data="heroComponent()">
        <div class="space-y-8" data-aos="fade-up" data-aos-duration="1000">
            <!-- Main Headline -->
            <h1 class="text-5xl md:text-7xl lg:text-8xl font-space font-bold text-white leading-tight">
                <span x-text="heroData.headline || 'We Only Get Paid'"></span><br>
                <span class="text-amber-500" x-text="heroData.highlight || 'When You See Results'"></span>
            </h1>

            <!-- Subheadline -->
            <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed" 
               x-text="heroData.subheadline || 'Performance-based CRO agency in the GCC. We optimize your website to increase conversions and revenue.'">
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 pt-8">
                <button @click="$parent.openVideoModal(heroData.video_url)" 
                        class="bg-amber-500 hover:bg-amber-600 text-black px-8 py-4 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 inline-flex items-center space-x-3">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="heroData.cta_primary || 'Watch Success Stories'"></span>
                </button>

                <button @click="scrollToCalculator()" 
                        class="bg-black/50 hover:bg-black/70 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 inline-flex items-center space-x-3 backdrop-blur-sm border-2 border-white/20">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span x-text="heroData.cta_secondary || 'Calculate Your ROI'"></span>
                </button>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </div>
</section>

<!-- Results Showcase Section -->
<section id="results" class="py-20 bg-gray-50" x-data="resultsComponent()">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-space font-bold text-gray-900 mb-6" 
                x-text="sectionData.title || 'Real Results for Real Businesses'"></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" 
               x-text="sectionData.description || 'See how we\'ve helped businesses across the GCC increase their conversion rates and revenue.'"></p>
        </div>

        <!-- Results Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <template x-for="(result, index) in results" :key="index">
                <div class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow" 
                     data-aos="fade-up" 
                     :data-aos-delay="index * 100">
                    <div class="text-4xl md:text-5xl font-bold text-amber-500 mb-2">
                        <span class="count-up" :data-target="result.value" x-text="result.value"></span>
                        <span x-text="result.suffix"></span>
                    </div>
                    <p class="text-gray-600 font-medium" x-text="result.label"></p>
                </div>
            </template>
        </div>

        <!-- Dashboard Preview -->
        <div class="relative" data-aos="fade-up" data-aos-delay="200">
            <div class="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 shadow-2xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-white">Live Dashboard Preview</h3>
                    <div class="flex space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                </div>
                
                <!-- Dashboard Content -->
                <div class="bg-white rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600 count-up" data-target="247.5">247.5</div>
                            <p class="text-gray-600">Conversion Rate %</p>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600 count-up" data-target="1.2">1.2</div>
                            <p class="text-gray-600">M Revenue Increase</p>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600 count-up" data-target="89">89</div>
                            <p class="text-gray-600">Days to Results</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trusted Brands Section -->
<section id="brands" class="py-16 bg-white" x-data="brandsComponent()">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-3xl md:text-4xl font-space font-bold text-gray-900 mb-4" 
                x-text="sectionData.title || 'Trusted by Leading Brands'"></h2>
            <p class="text-lg text-gray-600" 
               x-text="sectionData.description || 'Join hundreds of successful businesses that trust us with their growth.'"></p>
        </div>

        <!-- Brands Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
            <template x-for="(brand, index) in brands" :key="brand.id">
                <div class="flex items-center justify-center p-4 grayscale hover:grayscale-0 transition-all duration-300" 
                     data-aos="fade-up" 
                     :data-aos-delay="index * 50">
                    <img :src="brand.logo_url" 
                         :alt="brand.name" 
                         class="max-h-12 w-auto object-contain">
                </div>
            </template>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section id="testimonials" class="py-20 bg-gray-50" x-data="testimonialsComponent()">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-space font-bold text-gray-900 mb-6" 
                x-text="sectionData.title || 'What Our Clients Say'"></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" 
               x-text="sectionData.description || 'Real feedback from real clients who have seen real results.'"></p>
        </div>

        <!-- Testimonials Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <template x-for="(testimonial, index) in testimonials" :key="testimonial.id">
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow" 
                     data-aos="fade-up" 
                     :data-aos-delay="index * 100">
                    <!-- Rating -->
                    <div class="flex items-center mb-4">
                        <template x-for="i in 5" :key="i">
                            <svg class="w-5 h-5 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </template>
                    </div>

                    <!-- Testimonial Content -->
                    <blockquote class="text-gray-700 mb-6 italic" x-text="testimonial.content"></blockquote>

                    <!-- Author -->
                    <div class="flex items-center">
                        <img :src="testimonial.author_image_url || '/images/default-avatar.jpg'" 
                             :alt="testimonial.author_name" 
                             class="w-12 h-12 rounded-full object-cover mr-4">
                        <div>
                            <div class="font-semibold text-gray-900" x-text="testimonial.author_name"></div>
                            <div class="text-gray-600 text-sm" x-text="testimonial.author_position + ', ' + testimonial.company_name"></div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</section>

<!-- Industries Section -->
<section id="industries" class="py-20 bg-white" x-data="industriesComponent()">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16" data-aos="fade-up">
            <h2 class="text-4xl md:text-5xl font-space font-bold text-gray-900 mb-6" 
                x-text="sectionData.title || 'Cross-Industry Expertise'"></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto" 
               x-text="sectionData.description || 'We understand the unique challenges and opportunities across different industries.'"></p>
        </div>

        <!-- Industries Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            <template x-for="(industry, index) in industries" :key="industry.id">
                <div class="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors group" 
                     data-aos="fade-up" 
                     :data-aos-delay="index * 50">
                    <div class="w-16 h-16 mx-auto mb-4 bg-amber-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                        <i :class="industry.icon_class || 'fas fa-industry'" class="text-2xl text-white"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2" x-text="industry.name"></h3>
                    <p class="text-gray-600 text-sm" x-text="industry.description"></p>
                </div>
            </template>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<!-- Additional scripts can be added here -->
@endsection
