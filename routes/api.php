<?php

use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\ContentController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API routes for content
Route::prefix('v1')->group(function () {
    // Pages and sections
    Route::get('/pages', [PageController::class, 'index']);
    Route::get('/pages/{slug}', [PageController::class, 'show']);
    
    // Content types
    Route::get('/brands', [ContentController::class, 'brands']);
    Route::get('/testimonials', [ContentController::class, 'testimonials']);
    Route::get('/industries', [ContentController::class, 'industries']);
    Route::get('/services', [ContentController::class, 'services']);
    Route::get('/settings', [ContentController::class, 'settings']);
});

// Language switching endpoint
Route::post('/language', function (Request $request) {
    $locale = $request->input('locale', 'en');
    
    if (in_array($locale, ['en', 'ar'])) {
        session(['locale' => $locale]);
        app()->setLocale($locale);
        
        return response()->json([
            'success' => true,
            'locale' => $locale,
            'message' => 'Language switched successfully'
        ]);
    }
    
    return response()->json([
        'success' => false,
        'message' => 'Invalid locale'
    ], 400);
});
