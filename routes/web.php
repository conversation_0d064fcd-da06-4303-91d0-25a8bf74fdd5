<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Language switching route
Route::post('/language', function (Request $request) {
    $locale = $request->input('locale', 'en');

    if (in_array($locale, ['en', 'ar'])) {
        session(['locale' => $locale]);
        app()->setLocale($locale);

        return response()->json([
            'success' => true,
            'locale' => $locale,
            'message' => 'Language switched successfully'
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Invalid locale'
    ], 400);
});

// Main landing page routes
Route::get('/', function () {
    return view('welcome');
});

// Optional: Language-specific routes
Route::get('/{locale}', function ($locale) {
    if (in_array($locale, ['en', 'ar'])) {
        app()->setLocale($locale);
        session(['locale' => $locale]);
        return view('welcome');
    }

    abort(404);
})->where('locale', 'en|ar');
