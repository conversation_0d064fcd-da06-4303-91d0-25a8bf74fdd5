<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\Section;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Home Page
        $homePage = Page::firstOrCreate(
            ['slug' => 'home'],
            [
            'title' => [
                'en' => 'Optimizers - Performance-Based CRO Agency',
                'ar' => 'أوبتيمايزرز - وكالة تحسين معدل التحويل القائمة على الأداء'
            ],
            'slug' => 'home',
            'meta_description' => [
                'en' => 'Performance-based CRO agency in the GCC. We only get paid when you see results. Increase your conversion rates and revenue with our proven strategies.',
                'ar' => 'وكالة تحسين معدل التحويل القائمة على الأداء في دول الخليج. نحن نحصل على أجرنا فقط عندما ترى النتائج. زد معدلات التحويل والإيرادات باستراتيجياتنا المثبتة.'
            ],
            'is_active' => true,
            'sort_order' => 1
            ]
        );

        // Create Hero Section
        Section::create([
            'page_id' => $homePage->id,
            'type' => 'hero',
            'title' => [
                'en' => 'Hero Section',
                'ar' => 'القسم الرئيسي'
            ],
            'content' => [
                'en' => [
                    'headline' => 'We Only Get Paid',
                    'highlight' => 'When You See Results',
                    'subheadline' => 'Performance-based CRO agency in the GCC. We optimize your website to increase conversions and revenue.',
                    'cta_primary' => 'Watch Success Stories',
                    'cta_secondary' => 'Calculate Your ROI',
                    'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                    'background_video' => 'https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1c9a91a20&profile_id=139&oauth2_token_id=57447761'
                ],
                'ar' => [
                    'headline' => 'نحن نحصل على أجرنا فقط',
                    'highlight' => 'عندما ترى النتائج',
                    'subheadline' => 'وكالة تحسين معدل التحويل القائمة على الأداء في دول الخليج. نحن نحسن موقعك الإلكتروني لزيادة التحويلات والإيرادات.',
                    'cta_primary' => 'شاهد قصص النجاح',
                    'cta_secondary' => 'احسب عائد الاستثمار',
                    'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                    'background_video' => 'https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1c9a91a20&profile_id=139&oauth2_token_id=57447761'
                ]
            ],
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create Results Section
        Section::create([
            'page_id' => $homePage->id,
            'type' => 'results',
            'title' => [
                'en' => 'Results Showcase',
                'ar' => 'عرض النتائج'
            ],
            'content' => [
                'en' => [
                    'title' => 'Real Results for Real Businesses',
                    'description' => 'See how we\'ve helped businesses across the GCC increase their conversion rates and revenue.',
                    'results' => [
                        ['value' => 247, 'suffix' => '%', 'label' => 'Average Conversion Increase'],
                        ['value' => 1.2, 'suffix' => 'M', 'label' => 'Revenue Generated'],
                        ['value' => 89, 'suffix' => '', 'label' => 'Days to See Results'],
                        ['value' => 150, 'suffix' => '+', 'label' => 'Happy Clients']
                    ]
                ],
                'ar' => [
                    'title' => 'نتائج حقيقية لشركات حقيقية',
                    'description' => 'شاهد كيف ساعدنا الشركات في دول الخليج على زيادة معدلات التحويل والإيرادات.',
                    'results' => [
                        ['value' => 247, 'suffix' => '%', 'label' => 'متوسط زيادة التحويل'],
                        ['value' => 1.2, 'suffix' => 'م', 'label' => 'الإيرادات المحققة'],
                        ['value' => 89, 'suffix' => '', 'label' => 'أيام لرؤية النتائج'],
                        ['value' => 150, 'suffix' => '+', 'label' => 'عملاء سعداء']
                    ]
                ]
            ],
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create CTA Section
        Section::create([
            'page_id' => $homePage->id,
            'type' => 'cta',
            'title' => [
                'en' => 'Call to Action',
                'ar' => 'دعوة للعمل'
            ],
            'content' => [
                'en' => [
                    'title' => 'Ready to Increase Your Conversions?',
                    'description' => 'Let\'s discuss how we can help you achieve measurable results with our performance-based approach.',
                    'cta_button' => 'Start Your CRO Journey',
                    'cta_url' => '#contact',
                    'secondary_cta' => 'Calculate ROI',
                    'secondary_url' => '#calculator'
                ],
                'ar' => [
                    'title' => 'هل أنت مستعد لزيادة تحويلاتك؟',
                    'description' => 'دعنا نناقش كيف يمكننا مساعدتك في تحقيق نتائج قابلة للقياس بنهجنا القائم على الأداء.',
                    'cta_button' => 'ابدأ رحلة تحسين التحويل',
                    'cta_url' => '#contact',
                    'secondary_cta' => 'احسب العائد',
                    'secondary_url' => '#calculator'
                ]
            ],
            'is_active' => true,
            'sort_order' => 3
        ]);
    }
}
