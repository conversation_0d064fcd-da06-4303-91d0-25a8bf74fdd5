<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Brand;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            [
                'name' => [
                    'en' => 'Saudi Aramco',
                    'ar' => 'أرامكو السعودية'
                ],
                'description' => [
                    'en' => 'Leading energy company in the Middle East',
                    'ar' => 'شركة الطاقة الرائدة في الشرق الأوسط'
                ],
                'website' => 'https://www.aramco.com',
                'logo_url' => 'https://via.placeholder.com/200x100/1a365d/ffffff?text=Aramco',
                'is_featured' => true,
                'sort_order' => 1
            ],
            [
                'name' => [
                    'en' => 'Emirates Airlines',
                    'ar' => 'طيران الإمارات'
                ],
                'description' => [
                    'en' => 'Premium airline connecting the world',
                    'ar' => 'شركة طيران متميزة تربط العالم'
                ],
                'website' => 'https://www.emirates.com',
                'logo_url' => 'https://via.placeholder.com/200x100/dc2626/ffffff?text=Emirates',
                'is_featured' => true,
                'sort_order' => 2
            ],
            [
                'name' => [
                    'en' => 'Qatar Airways',
                    'ar' => 'الخطوط الجوية القطرية'
                ],
                'description' => [
                    'en' => 'Award-winning airline of Qatar',
                    'ar' => 'شركة الطيران الحائزة على جوائز في قطر'
                ],
                'website' => 'https://www.qatarairways.com',
                'logo_url' => 'https://via.placeholder.com/200x100/7c2d12/ffffff?text=Qatar+Airways',
                'is_featured' => true,
                'sort_order' => 3
            ],
            [
                'name' => [
                    'en' => 'Etisalat',
                    'ar' => 'اتصالات'
                ],
                'description' => [
                    'en' => 'Leading telecommunications provider',
                    'ar' => 'مزود الاتصالات الرائد'
                ],
                'website' => 'https://www.etisalat.ae',
                'logo_url' => 'https://via.placeholder.com/200x100/059669/ffffff?text=Etisalat',
                'is_featured' => true,
                'sort_order' => 4
            ],
            [
                'name' => [
                    'en' => 'ADNOC',
                    'ar' => 'أدنوك'
                ],
                'description' => [
                    'en' => 'Abu Dhabi National Oil Company',
                    'ar' => 'شركة بترول أبوظبي الوطنية'
                ],
                'website' => 'https://www.adnoc.ae',
                'logo_url' => 'https://via.placeholder.com/200x100/1e40af/ffffff?text=ADNOC',
                'is_featured' => true,
                'sort_order' => 5
            ],
            [
                'name' => [
                    'en' => 'Almarai',
                    'ar' => 'المراعي'
                ],
                'description' => [
                    'en' => 'Leading food and beverage company',
                    'ar' => 'شركة الأغذية والمشروبات الرائدة'
                ],
                'website' => 'https://www.almarai.com',
                'logo_url' => 'https://via.placeholder.com/200x100/16a34a/ffffff?text=Almarai',
                'is_featured' => true,
                'sort_order' => 6
            ],
            [
                'name' => [
                    'en' => 'Majid Al Futtaim',
                    'ar' => 'ماجد الفطيم'
                ],
                'description' => [
                    'en' => 'Leading shopping mall and retail company',
                    'ar' => 'شركة مراكز التسوق والتجزئة الرائدة'
                ],
                'website' => 'https://www.majidalfuttaim.com',
                'logo_url' => 'https://via.placeholder.com/200x100/ea580c/ffffff?text=MAF',
                'is_featured' => true,
                'sort_order' => 7
            ],
            [
                'name' => [
                    'en' => 'Noon',
                    'ar' => 'نون'
                ],
                'description' => [
                    'en' => 'Leading e-commerce platform in the Middle East',
                    'ar' => 'منصة التجارة الإلكترونية الرائدة في الشرق الأوسط'
                ],
                'website' => 'https://www.noon.com',
                'logo_url' => 'https://via.placeholder.com/200x100/fbbf24/000000?text=noon',
                'is_featured' => true,
                'sort_order' => 8
            ]
        ];

        foreach ($brands as $brandData) {
            Brand::create($brandData);
        }
    }
}
