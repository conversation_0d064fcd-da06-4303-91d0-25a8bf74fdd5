@keyframes scroll-horizontal {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-50%);
    }
}

.scrolling-icons {
    animation: scroll-horizontal 40s linear infinite;
}

.gradient-text {
    background: linear-gradient(135deg, #f59e0b, #f97316);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-glass-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-bg {
    position: relative;
    overflow: hidden;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1;
}

.hero-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.03"><circle cx="30" cy="30" r="1"/></g></svg>');
    z-index: 2;
}

.hero-bg video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0; /* Behind the overlay */
}

.hero-bg > .relative {
    z-index: 2; /* Content on top of the overlay */
}

.scrolling-wrapper {
    animation: scroll 20s linear infinite;
}
.scrolling-wrapper div{
    max-width:300px;
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(calc(-100% / 2)); }
}

.parallax {
    transform: translateZ(0);
    will-change: transform;
}

.mobile-mockup {
    position: relative;
    width: 200px; /* Reduced from 250px */
    height: 400px; /* Reduced from 500px */
    background: linear-gradient(145deg, #1f2937, #374151);
    border-radius: 30px;
    padding: 15px; /* Adjusted padding */
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
}

.mobile-screen {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}
