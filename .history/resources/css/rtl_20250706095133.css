/* RTL Styles for Arabic */

/* Import custom.css and override with RTL specific styles */
@import url('custom.css');

/* RTL Keyframes */
@keyframes scroll-horizontal-rtl {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(50%);
    }
}

@keyframes scroll-rtl {
    0% { transform: translateX(0); }
    100% { transform: translateX(calc(100% / 2)); }
}

/* RTL Scrolling animations */
.scrolling-icons {
    animation: scroll-horizontal-rtl 40s linear infinite;
}

.scrolling-wrapper {
    animation: scroll-rtl 20s linear infinite;
}

/* RTL Layout overrides */
[dir="rtl"] {
    font-family: 'Tajawal', 'Inter', sans-serif;
}

/* Navigation RTL */
[dir="rtl"] .flex.items-center.space-x-3 {
    flex-direction: row-reverse;
}

[dir="rtl"] .flex.items-center.space-x-10 {
    flex-direction: row-reverse;
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-10 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(2.5rem * var(--tw-space-x-reverse));
    margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Hero Section RTL */
[dir="rtl"] .hero-bg .grid.lg\\:grid-cols-2 {
    direction: rtl;
}

[dir="rtl"] .hero-bg .space-y-8 {
    text-align: right;
}

/* Button RTL */
[dir="rtl"] .inline-flex.items-center.space-x-3 {
    flex-direction: row-reverse;
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Stats and Cards RTL */
[dir="rtl"] .flex.items-center.space-x-4 {
    flex-direction: row-reverse;
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Testimonials RTL */
[dir="rtl"] .flex.items-center.mb-6 {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .ml-4 {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
    margin-right: 0;
    margin-left: 1rem;
}

/* Cross-Industry Section RTL */
[dir="rtl"] .scrolling-icons {
    direction: rtl;
}

[dir="rtl"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Footer RTL */
[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Text alignment RTL */
[dir="rtl"] .text-left {
    text-align: right;
}

[dir="rtl"] .text-right {
    text-align: left;
}

/* Gradients RTL */
[dir="rtl"] .bg-gradient-to-r {
    background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

[dir="rtl"] .bg-gradient-to-l {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Positioning RTL */
[dir="rtl"] .left-0 {
    left: auto;
    right: 0;
}

[dir="rtl"] .right-0 {
    right: auto;
    left: 0;
}

[dir="rtl"] .left-1\\/2 {
    left: auto;
    right: 50%;
}

[dir="rtl"] .-translate-x-1\\/2 {
    transform: translateX(50%);
}

/* Mobile responsive RTL */
@media (max-width: 768px) {
    [dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]),
    [dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]),
    [dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]),
    [dir="rtl"] .space-x-8 > :not([hidden]) ~ :not([hidden]),
    [dir="rtl"] .space-x-10 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-x-reverse: 1;
    }
}
