// SPA Main Application
function spaApp() {
    return {
        // State
        loading: false,
        loadingText: 'Loading...',
        locale: window.spaConfig.locale || 'en',
        pageData: {},
        sections: [],
        brands: [],
        testimonials: [],
        industries: [],
        services: [],
        settings: {},
        showVideoModal: false,
        currentVideoUrl: '',
        
        // Translations
        translations: {
            en: {
                watchStories: 'Watch Success Stories',
                loading: 'Loading...',
                switchingLanguage: 'Switching Language...',
                loadingContent: 'Loading Content...',
                services: 'Services',
                calculator: 'ROI Calculator',
                team: 'Team',
                caseStudies: 'Case Studies',
                contact: 'Contact'
            },
            ar: {
                watchStories: 'شاهد قصص النجاح',
                loading: 'جاري التحميل...',
                switchingLanguage: 'تغيير اللغة...',
                loadingContent: 'تحميل المحتوى...',
                services: 'الخدمات',
                calculator: 'حاسبة العائد',
                team: 'الفريق',
                caseStudies: 'دراسات الحالة',
                contact: 'اتصل بنا'
            }
        },

        // Initialize
        async init() {
            console.log('SPA App Initializing...');
            
            // Set initial loading text
            this.updateLoadingText('loading');
            
            // Load initial data
            await this.loadPageData();
            await this.loadAllContent();
            
            // Initialize AOS
            this.initializeAnimations();
            
            // Setup scroll animations
            this.setupScrollAnimations();
            
            console.log('SPA App Initialized');
        },

        // API Methods
        async apiCall(endpoint, options = {}) {
            const url = `${window.spaConfig.apiUrl}${endpoint}?locale=${this.locale}`;
            
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.spaConfig.csrfToken,
                        'Accept': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        },

        async loadPageData(slug = 'home') {
            try {
                this.setLoading(true, 'loadingContent');
                const response = await this.apiCall(`/pages/${slug}`);
                
                if (response.success && response.data) {
                    this.pageData = response.data;
                    this.sections = response.data.sections || [];
                }
            } catch (error) {
                console.error('Failed to load page data:', error);
                // Fallback to static content
                this.pageData = {
                    title: 'Optimizers - Performance-Based CRO Agency',
                    meta_description: 'Performance-based CRO agency in the GCC.'
                };
            } finally {
                this.setLoading(false);
            }
        },

        async loadAllContent() {
            try {
                // Load all content types in parallel
                const [brandsRes, testimonialsRes, industriesRes, servicesRes, settingsRes] = await Promise.all([
                    this.apiCall('/brands'),
                    this.apiCall('/testimonials'),
                    this.apiCall('/industries'),
                    this.apiCall('/services'),
                    this.apiCall('/settings')
                ]);

                if (brandsRes.success) this.brands = brandsRes.data;
                if (testimonialsRes.success) this.testimonials = testimonialsRes.data;
                if (industriesRes.success) this.industries = industriesRes.data;
                if (servicesRes.success) this.services = servicesRes.data;
                if (settingsRes.success) this.settings = settingsRes.data;

            } catch (error) {
                console.error('Failed to load content:', error);
            }
        },

        // Language Methods
        async toggleLanguage() {
            const newLocale = this.locale === 'en' ? 'ar' : 'en';
            await this.switchLanguage(newLocale);
        },

        async switchLanguage(newLocale) {
            if (newLocale === this.locale) return;

            try {
                this.setLoading(true, 'switchingLanguage');
                
                // Update locale
                this.locale = newLocale;
                
                // Update document attributes
                document.documentElement.lang = newLocale;
                document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';
                
                // Call language switching API
                await fetch('/language', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.spaConfig.csrfToken,
                    },
                    body: JSON.stringify({ locale: newLocale })
                });

                // Reload content with new language
                await this.loadPageData();
                await this.loadAllContent();

                // Reinitialize animations
                this.initializeAnimations();

            } catch (error) {
                console.error('Failed to switch language:', error);
            } finally {
                this.setLoading(false);
            }
        },

        // UI Methods
        setLoading(state, textKey = 'loading') {
            this.loading = state;
            this.loadingText = this.getTranslation(textKey);
        },

        updateLoadingText(textKey) {
            this.loadingText = this.getTranslation(textKey);
        },

        getTranslation(key) {
            return this.translations[this.locale]?.[key] || this.translations.en[key] || key;
        },

        openVideoModal(videoUrl = 'https://www.youtube.com/embed/dQw4w9WgXcQ') {
            this.currentVideoUrl = videoUrl;
            this.showVideoModal = true;
            document.body.style.overflow = 'hidden';
        },

        closeVideoModal() {
            this.showVideoModal = false;
            this.currentVideoUrl = '';
            document.body.style.overflow = 'auto';
        },

        scrollToSection(event, href) {
            if (href.startsWith('#')) {
                event.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        },

        // Animation Methods
        initializeAnimations() {
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 1000,
                    easing: 'ease-in-out-quart',
                    once: true,
                    offset: 100
                });
            }
        },

        setupScrollAnimations() {
            // Counter animations
            this.setupCounterAnimations();
            
            // Parallax effects
            this.setupParallaxEffects();
        },

        setupCounterAnimations() {
            if (typeof CountUp === 'undefined') return;

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.target.classList.contains('count-up')) {
                        const target = parseFloat(entry.target.getAttribute('data-target'));
                        const isDecimal = target % 1 !== 0;

                        const countUp = new CountUp(entry.target, target, {
                            duration: 2,
                            decimal: isDecimal ? '.' : '',
                            decimalPlaces: isDecimal ? 1 : 0
                        });

                        if (!countUp.error) {
                            countUp.start();
                        }
                        
                        observer.unobserve(entry.target);
                    }
                });
            });

            document.querySelectorAll('.count-up').forEach(el => {
                observer.observe(el);
            });
        },

        setupParallaxEffects() {
            const parallaxElements = document.querySelectorAll('.parallax');
            
            if (parallaxElements.length === 0) return;

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    const yPos = -(scrollTop * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });
        }
    }
}

// Header Component
function headerComponent() {
    return {
        navigation: [],

        init() {
            this.setupNavigation();
        },

        setupNavigation() {
            this.navigation = [
                { id: 'services', href: '#services', label: this.$parent.getTranslation('services') },
                { id: 'calculator', href: '#calculator', label: this.$parent.getTranslation('calculator') },
                { id: 'team', href: '#team', label: this.$parent.getTranslation('team') },
                { id: 'case-studies', href: '#case-studies', label: this.$parent.getTranslation('caseStudies') },
                { id: 'contact', href: '#contact', label: this.$parent.getTranslation('contact') }
            ];
        },

        scrollToSection(event, href) {
            this.$parent.scrollToSection(event, href);
        },

        toggleLanguage() {
            this.$parent.toggleLanguage();
        },

        openVideoModal() {
            this.$parent.openVideoModal();
        }
    }
}

// Hero Component
function heroComponent() {
    return {
        heroData: {},

        init() {
            this.loadHeroData();
        },

        loadHeroData() {
            // Get hero data from parent sections or use defaults
            const heroSection = this.$parent.sections.find(s => s.type === 'hero');
            if (heroSection) {
                this.heroData = heroSection.content;
            } else {
                this.heroData = {
                    headline: 'We Only Get Paid',
                    highlight: 'When You See Results',
                    subheadline: 'Performance-based CRO agency in the GCC. We optimize your website to increase conversions and revenue.',
                    cta_primary: 'Watch Success Stories',
                    cta_secondary: 'Calculate Your ROI',
                    video_url: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
                };
            }
        },

        scrollToCalculator() {
            this.$parent.scrollToSection(event, '#calculator');
        }
    }
}

// Results Component
function resultsComponent() {
    return {
        sectionData: {},
        results: [],

        init() {
            this.loadResultsData();
        },

        loadResultsData() {
            const resultsSection = this.$parent.sections.find(s => s.type === 'results');
            if (resultsSection) {
                this.sectionData = resultsSection.content;
                this.results = resultsSection.content.results || [];
            } else {
                this.sectionData = {
                    title: 'Real Results for Real Businesses',
                    description: 'See how we\'ve helped businesses across the GCC increase their conversion rates and revenue.'
                };
                this.results = [
                    { value: 247, suffix: '%', label: 'Average Conversion Increase' },
                    { value: 1.2, suffix: 'M', label: 'Revenue Generated' },
                    { value: 89, suffix: '', label: 'Days to See Results' },
                    { value: 150, suffix: '+', label: 'Happy Clients' }
                ];
            }
        }
    }
}

// Brands Component
function brandsComponent() {
    return {
        sectionData: {},
        brands: [],

        init() {
            this.loadBrandsData();
        },

        loadBrandsData() {
            this.brands = this.$parent.brands || [];
            this.sectionData = {
                title: 'Trusted by Leading Brands',
                description: 'Join hundreds of successful businesses that trust us with their growth.'
            };
        }
    }
}

// Testimonials Component
function testimonialsComponent() {
    return {
        sectionData: {},
        testimonials: [],

        init() {
            this.loadTestimonialsData();
        },

        loadTestimonialsData() {
            this.testimonials = this.$parent.testimonials || [];
            this.sectionData = {
                title: 'What Our Clients Say',
                description: 'Real feedback from real clients who have seen real results.'
            };
        }
    }
}

// Industries Component
function industriesComponent() {
    return {
        sectionData: {},
        industries: [],

        init() {
            this.loadIndustriesData();
        },

        loadIndustriesData() {
            this.industries = this.$parent.industries || [];
            this.sectionData = {
                title: 'Cross-Industry Expertise',
                description: 'We understand the unique challenges and opportunities across different industries.'
            };
        }
    }
}
