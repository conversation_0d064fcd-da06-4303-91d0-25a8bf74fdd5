// SPA Main Application
function spaApp() {
    return {
        // State
        loading: false,
        loadingText: 'Loading...',
        locale: window.spaConfig.locale || 'en',
        pageData: {},
        sections: [],
        brands: [],
        testimonials: [],
        industries: [],
        services: [],
        settings: {},
        showVideoModal: false,
        currentVideoUrl: '',
        
        // Translations (using global translation system)
        translations: window.AppTranslations || {},

        // Initialize
        async init() {
            console.log('SPA App Initializing...');

            // Detect and set initial locale
            this.detectInitialLocale();

            // Set initial loading text
            this.updateLoadingText('loading');

            // Load initial data
            await this.loadPageData();
            await this.loadAllContent();

            // Initialize AOS
            this.initializeAnimations();

            // Setup scroll animations
            this.setupScrollAnimations();

            // Setup browser back/forward handling
            this.setupHistoryHandling();

            console.log('SPA App Initialized');
        },

        detectInitialLocale() {
            // Check URL first
            const pathLocale = this.getLocaleFromPath();
            if (pathLocale) {
                this.locale = pathLocale;
                return;
            }

            // Check browser language
            const browserLang = navigator.language || navigator.userLanguage;
            if (browserLang.startsWith('ar')) {
                this.locale = 'ar';
            } else {
                this.locale = 'en';
            }

            // Update document attributes
            document.documentElement.lang = this.locale;
            document.documentElement.dir = this.locale === 'ar' ? 'rtl' : 'ltr';
        },

        getLocaleFromPath() {
            const path = window.location.pathname;
            const match = path.match(/^\/(en|ar)/);
            return match ? match[1] : null;
        },

        setupHistoryHandling() {
            window.addEventListener('popstate', (event) => {
                const newLocale = this.getLocaleFromPath();
                if (newLocale && newLocale !== this.locale) {
                    this.switchLanguage(newLocale);
                }
            });
        },

        // API Methods
        async apiCall(endpoint, options = {}) {
            const url = `${window.spaConfig.apiUrl}${endpoint}?locale=${this.locale}`;
            
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.spaConfig.csrfToken,
                        'Accept': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        },

        async loadPageData(slug = 'home') {
            try {
                this.setLoading(true, 'loadingContent');
                const response = await this.apiCall(`/pages/${slug}`);

                if (response.success && response.data) {
                    this.pageData = response.data;
                    this.sections = response.data.sections || [];

                    // Dispatch event to notify child components
                    window.dispatchEvent(new CustomEvent('pageDataLoaded', {
                        detail: { pageData: this.pageData, sections: this.sections }
                    }));
                }
            } catch (error) {
                console.error('Failed to load page data:', error);
                // Fallback to static content
                this.pageData = {
                    title: 'Optimizers - Performance-Based CRO Agency',
                    meta_description: 'Performance-based CRO agency in the GCC.'
                };
            } finally {
                this.setLoading(false);
            }
        },

        async loadAllContent() {
            try {
                // Load all content types in parallel
                const [brandsRes, testimonialsRes, industriesRes, servicesRes, settingsRes] = await Promise.all([
                    this.apiCall('/brands'),
                    this.apiCall('/testimonials'),
                    this.apiCall('/industries'),
                    this.apiCall('/services'),
                    this.apiCall('/settings')
                ]);

                if (brandsRes.success) this.brands = brandsRes.data;
                if (testimonialsRes.success) this.testimonials = testimonialsRes.data;
                if (industriesRes.success) this.industries = industriesRes.data;
                if (servicesRes.success) this.services = servicesRes.data;
                if (settingsRes.success) this.settings = settingsRes.data;

                // Dispatch event to notify child components
                window.dispatchEvent(new CustomEvent('contentDataLoaded', {
                    detail: {
                        brands: this.brands,
                        testimonials: this.testimonials,
                        industries: this.industries,
                        services: this.services,
                        settings: this.settings
                    }
                }));

            } catch (error) {
                console.error('Failed to load content:', error);
            }
        },

        // Language Methods
        async toggleLanguage() {
            const newLocale = this.locale === 'en' ? 'ar' : 'en';
            await this.switchLanguage(newLocale);
        },

        async switchLanguage(newLocale) {
            if (newLocale === this.locale) return;

            try {
                this.setLoading(true, 'switchingLanguage');

                // Update locale
                this.locale = newLocale;

                // Update document attributes
                document.documentElement.lang = newLocale;
                document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';

                // Update body classes for font switching
                document.body.className = document.body.className.replace(/font-(inter|tajawal)/g, '');
                document.body.classList.add(newLocale === 'ar' ? 'font-tajawal' : 'font-inter');

                // Update URL without page reload
                const currentPath = window.location.pathname;
                let newPath;

                if (currentPath === '/spa') {
                    newPath = `/${newLocale}/spa`;
                } else if (currentPath.match(/^\/(en|ar)\/spa$/)) {
                    newPath = `/${newLocale}/spa`;
                } else if (currentPath === '/') {
                    newPath = `/${newLocale}`;
                } else if (currentPath.match(/^\/(en|ar)$/)) {
                    newPath = `/${newLocale}`;
                } else {
                    newPath = currentPath;
                }

                // Update URL
                window.history.pushState({}, '', newPath);

                // Call language switching API
                await fetch('/language', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.spaConfig.csrfToken,
                    },
                    body: JSON.stringify({ locale: newLocale })
                });

                // Reload content with new language
                await this.loadPageData();
                await this.loadAllContent();

                // Update navigation labels
                this.updateNavigationLabels();

                // Reinitialize animations
                this.initializeAnimations();

                // Trigger custom event for other components
                window.dispatchEvent(new CustomEvent('languageChanged', {
                    detail: { locale: newLocale }
                }));

            } catch (error) {
                console.error('Failed to switch language:', error);
            } finally {
                this.setLoading(false);
            }
        },

        updateNavigationLabels() {
            // This will be called by header component to update navigation
            window.dispatchEvent(new CustomEvent('updateNavigation'));
        },

        // UI Methods
        setLoading(state, textKey = 'loading') {
            this.loading = state;
            this.loadingText = this.getTranslation(textKey);
        },

        updateLoadingText(textKey) {
            this.loadingText = this.getTranslation(textKey);
        },

        getTranslation(key) {
            return window.getTranslation(key, this.locale);
        },

        openVideoModal(videoUrl = 'https://www.youtube.com/embed/dQw4w9WgXcQ') {
            this.currentVideoUrl = videoUrl;
            this.showVideoModal = true;
            document.body.style.overflow = 'hidden';
        },

        closeVideoModal() {
            this.showVideoModal = false;
            this.currentVideoUrl = '';
            document.body.style.overflow = 'auto';
        },

        scrollToSection(event, href) {
            if (href.startsWith('#')) {
                event.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        },

        // Animation Methods
        initializeAnimations() {
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 1000,
                    easing: 'ease-in-out-quart',
                    once: true,
                    offset: 100
                });
            }
        },

        setupScrollAnimations() {
            // Counter animations
            this.setupCounterAnimations();
            
            // Parallax effects
            this.setupParallaxEffects();
        },

        setupCounterAnimations() {
            if (typeof CountUp === 'undefined') return;

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.target.classList.contains('count-up')) {
                        const target = parseFloat(entry.target.getAttribute('data-target'));
                        const isDecimal = target % 1 !== 0;

                        const countUp = new CountUp(entry.target, target, {
                            duration: 2,
                            decimal: isDecimal ? '.' : '',
                            decimalPlaces: isDecimal ? 1 : 0
                        });

                        if (!countUp.error) {
                            countUp.start();
                        }
                        
                        observer.unobserve(entry.target);
                    }
                });
            });

            document.querySelectorAll('.count-up').forEach(el => {
                observer.observe(el);
            });
        },

        setupParallaxEffects() {
            const parallaxElements = document.querySelectorAll('.parallax');
            
            if (parallaxElements.length === 0) return;

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    const yPos = -(scrollTop * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });
        }
    }
}

// Header Component
function headerComponent() {
    return {
        navigation: [],

        init() {
            this.setupNavigation();
            this.setupEventListeners();
        },

        setupNavigation() {
            this.navigation = [
                { id: 'services', href: '#services', label: this.$parent.getTranslation('services') },
                { id: 'calculator', href: '#calculator', label: this.$parent.getTranslation('calculator') },
                { id: 'team', href: '#team', label: this.$parent.getTranslation('team') },
                { id: 'case-studies', href: '#case-studies', label: this.$parent.getTranslation('caseStudies') },
                { id: 'contact', href: '#contact', label: this.$parent.getTranslation('contact') }
            ];
        },

        setupEventListeners() {
            // Listen for language changes
            window.addEventListener('languageChanged', () => {
                this.setupNavigation();
            });

            // Listen for navigation update requests
            window.addEventListener('updateNavigation', () => {
                this.setupNavigation();
            });
        },

        scrollToSection(event, href) {
            this.$parent.scrollToSection(event, href);
        },

        toggleLanguage() {
            this.$parent.toggleLanguage();
        },

        openVideoModal() {
            this.$parent.openVideoModal();
        }
    }
}

// Hero Component
function heroComponent() {
    return {
        heroData: {},

        init() {
            this.loadHeroData();

            // Listen for page data updates
            window.addEventListener('pageDataLoaded', () => {
                this.loadHeroData();
            });
        },

        loadHeroData() {
            // Get hero data from parent sections or use defaults
            const heroSection = this.$parent.sections.find(s => s.type === 'hero');
            if (heroSection) {
                this.heroData = heroSection.content;
            } else {
                this.heroData = {
                    headline: 'We Only Get Paid',
                    highlight: 'When You See Results',
                    subheadline: 'Performance-based CRO agency in the GCC. We optimize your website to increase conversions and revenue.',
                    cta_primary: 'Watch Success Stories',
                    cta_secondary: 'Calculate Your ROI',
                    video_url: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
                };
            }
        },

        scrollToCalculator() {
            this.$parent.scrollToSection(event, '#calculator');
        }
    }
}

// Results Component
function resultsComponent() {
    return {
        sectionData: {},
        results: [],

        init() {
            this.loadResultsData();

            // Listen for page data updates
            window.addEventListener('pageDataLoaded', () => {
                this.loadResultsData();
            });
        },

        loadResultsData() {
            const resultsSection = this.$parent.sections.find(s => s.type === 'results');
            if (resultsSection) {
                this.sectionData = resultsSection.content;
                this.results = resultsSection.content.results || [];
            } else {
                this.sectionData = {
                    title: 'Real Results for Real Businesses',
                    description: 'See how we\'ve helped businesses across the GCC increase their conversion rates and revenue.'
                };
                this.results = [
                    { value: 247, suffix: '%', label: 'Average Conversion Increase' },
                    { value: 1.2, suffix: 'M', label: 'Revenue Generated' },
                    { value: 89, suffix: '', label: 'Days to See Results' },
                    { value: 150, suffix: '+', label: 'Happy Clients' }
                ];
            }
        }
    }
}

// Brands Component
function brandsComponent() {
    return {
        sectionData: {},
        brands: [],

        init() {
            this.loadBrandsData();

            // Listen for content data updates
            window.addEventListener('contentDataLoaded', (event) => {
                this.brands = event.detail.brands || [];
            });
        },

        loadBrandsData() {
            this.brands = this.$parent.brands || [];
            this.sectionData = {
                title: 'Trusted by Leading Brands',
                description: 'Join hundreds of successful businesses that trust us with their growth.'
            };
        }
    }
}

// Testimonials Component
function testimonialsComponent() {
    return {
        sectionData: {},
        testimonials: [],

        init() {
            this.loadTestimonialsData();

            // Listen for content data updates
            window.addEventListener('contentDataLoaded', (event) => {
                this.testimonials = event.detail.testimonials || [];
            });
        },

        loadTestimonialsData() {
            this.testimonials = this.$parent.testimonials || [];
            this.sectionData = {
                title: 'What Our Clients Say',
                description: 'Real feedback from real clients who have seen real results.'
            };
        }
    }
}

// Industries Component
function industriesComponent() {
    return {
        sectionData: {},
        industries: [],

        init() {
            this.loadIndustriesData();

            // Listen for content data updates
            window.addEventListener('contentDataLoaded', (event) => {
                this.industries = event.detail.industries || [];
            });
        },

        loadIndustriesData() {
            this.industries = this.$parent.industries || [];
            this.sectionData = {
                title: 'Cross-Industry Expertise',
                description: 'We understand the unique challenges and opportunities across different industries.'
            };
        }
    }
}

// Services Component
function servicesComponent() {
    return {
        sectionData: {},
        services: [],

        init() {
            this.loadServicesData();

            // Listen for content data updates
            window.addEventListener('contentDataLoaded', (event) => {
                this.services = event.detail.services || [];
            });
        },

        loadServicesData() {
            this.services = this.$parent.services || [];
            this.sectionData = {
                title: 'Our Services',
                description: 'Comprehensive CRO services designed to maximize your conversion rates and revenue.'
            };
        }
    }
}
