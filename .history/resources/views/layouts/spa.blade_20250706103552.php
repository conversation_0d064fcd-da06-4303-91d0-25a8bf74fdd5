<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" x-data="spaApp()">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Dynamic Meta Tags -->
    <title x-text="pageData.title || 'Optimizers - Performance-Based CRO Agency'"></title>
    <meta name="description" x-bind:content="pageData.meta_description || 'Performance-based CRO agency in the GCC. We only get paid when you see results.'">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom CSS -->
    @vite(['resources/css/custom.css', 'resources/css/rtl.css'])

    <!-- Counter Animation Library -->
    <script src="https://cdn.jsdelivr.net/npm/countup.js@2.8.0/dist/countUp.umd.js"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'space': ['Space Grotesk', 'sans-serif'],
                        'inter': ['Inter', 'sans-serif'],
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>

    <!-- SPA Configuration -->
    <script>
        window.spaConfig = {
            apiUrl: '{{ url('/api/v1') }}',
            locale: '{{ app()->getLocale() }}',
            csrfToken: '{{ csrf_token() }}'
        };
    </script>
</head>
<body class="font-inter text-gray-900 bg-white" 
      x-bind:class="{ 'font-tajawal': locale === 'ar' }"
      x-bind:dir="locale === 'ar' ? 'rtl' : 'ltr'">
    
    <!-- Loading Overlay -->
    <div x-show="loading" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 shadow-xl">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                <span x-text="loadingText" class="text-gray-700"></span>
            </div>
        </div>
    </div>

    <!-- Header Component -->
    <header x-data="headerComponent()" class="fixed top-0 left-0 right-0 z-40 bg-black/80 backdrop-blur-sm">
        <nav class="mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-9 h-9 bg-amber-500 rounded-lg flex items-center justify-center">
                        <span class="text-black font-bold text-base">O</span>
                    </div>
                    <span class="text-white font-space font-bold text-xl tracking-wide">OPTIMIZERS</span>
                </div>

                <!-- Navigation -->
                <div class="hidden md:flex items-center space-x-10">
                    <template x-for="item in navigation" :key="item.id">
                        <a x-bind:href="item.href" 
                           x-text="item.label" 
                           @click="scrollToSection($event, item.href)"
                           class="text-gray-300 hover:text-white transition-colors font-normal text-sm cursor-pointer"></a>
                    </template>
                    
                    <!-- Language Switcher -->
                    <div @click="toggleLanguage()" 
                         class="flex items-center space-x-2 border border-gray-700 px-3 py-1.5 rounded-full cursor-pointer hover:border-gray-500 transition-colors">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                        <span x-text="locale === 'ar' ? 'English' : 'العربية'" class="text-gray-400 font-normal text-sm"></span>
                    </div>
                </div>

                <!-- CTA Button -->
                <button @click="openVideoModal()" 
                        class="bg-black/50 hover:bg-black/70 text-white px-8 py-4 rounded-lg font-semibold text-base transition-all transform hover:scale-105 inline-flex items-center space-x-3 backdrop-blur-sm border-2 border-white/20">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="translations.watchStories"></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Video Modal -->
    <div x-show="showVideoModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeVideoModal()"
         class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div @click.stop class="bg-white rounded-lg max-w-4xl w-full aspect-video">
            <iframe x-bind:src="currentVideoUrl" 
                    class="w-full h-full rounded-lg" 
                    frameborder="0" 
                    allowfullscreen></iframe>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intersection-observer@0.12.2/intersection-observer.js"></script>
    
    @yield('scripts')
</body>
</html>
