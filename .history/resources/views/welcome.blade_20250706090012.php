<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimizers - Performance-Based CRO Agency</title>
    <meta name="description" content="Performance-based CRO agency in the GCC. We only get paid when you see results. Increase your conversion rates with our proven optimization strategies.">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Counter Animation Library -->
    <script src="https://cdn.jsdelivr.net/npm/countup.js@2.8.0/dist/countUp.umd.js"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'space': ['Space Grotesk', 'sans-serif'],
                        'inter': ['Inter', 'sans-serif'],
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .gradient-text {
            background: linear-gradient(135deg, #f59e0b, #f97316);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark-glass-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: #1a1a1a;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1;
        }

        .hero-bg video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }

        .scrolling-wrapper {
            animation: scroll 20s linear infinite;
        }
        .scrolling-wrapper div{
            max-width:250px
        }

        @keyframes scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(calc(-100% / 2)); }
        }

        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }

        .mobile-mockup {
            position: relative;
            width: 200px; /* Reduced from 250px */
            height: 400px; /* Reduced from 500px */
            background: linear-gradient(145deg, #1f2937, #374151);
            border-radius: 30px;
            padding: 15px; /* Adjusted padding */
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="font-inter text-gray-900 overflow-x-hidden">

    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm">
        <nav class="mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-9 h-9 bg-amber-500 rounded-lg flex items-center justify-center">
                        <span class="text-black font-bold text-base">O</span>
                    </div>
                    <span class="text-white font-space font-bold text-xl tracking-wide">OPTIMIZERS</span>
                </div>

                <!-- Navigation -->
                <div class="hidden md:flex items-center space-x-10">
                    <a href="#services" class="text-gray-300 hover:text-white transition-colors font-normal text-sm">Services</a>
                    <a href="#calculator" class="text-gray-300 hover:text-white transition-colors font-normal text-sm">ROI Calculator</a>
                    <a href="#team" class="text-gray-300 hover:text-white transition-colors font-normal text-sm">Team</a>
                    <a href="#case-studies" class="text-gray-300 hover:text-white transition-colors font-normal text-sm">Case Studies</a>
                    <a href="#contact" class="text-gray-300 hover:text-white transition-colors font-normal text-sm">Contact</a>
                    <div class="flex items-center space-x-2 border border-gray-700 px-3 py-1.5 rounded-full">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                        <span class="text-gray-400 font-normal text-sm">العربية</span>
                    </div>
                </div>

                <!-- CTA Button -->
                <button class="bg-black/50 hover:bg-black/70 text-white px-8 py-4 rounded-lg font-semibold text-base transition-all transform hover:scale-105 inline-flex items-center space-x-3 backdrop-blur-sm border-2 border-white/20">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <span>Watch Success Stories</span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-bg min-h-screen flex items-center relative">
        <!-- Background Video -->
        <video
            autoplay
            muted
            loop
            playsinline
            class="absolute inset-0 w-full h-full object-cover"
        >
            <source src="https://anatta.io/_nuxt/videos/AnattaWebsiteReel.fac7890.webm" type="video/webm">
            Your browser does not support the video tag.
        </video>

        <!-- Content Container -->
        <div class="mx-auto px-14 relative z-10 pt-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Left Side - Content -->
                <div class="space-y-8" data-aos="fade-right">
                    <!-- Badge -->
                    <div class="inline-flex items-center bg-amber-500/20 border border-amber-500/30 rounded-full px-4 py-2 text-amber-500 text-sm backdrop-blur-sm">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        ✨ #1 CRO Agency for E-Commerce Platforms
                    </div>

                    <!-- Main Heading -->
                    <h1 class="text-5xl lg:text-6xl font-space font-bold text-white leading-tight">
                        Your Ads Bring<br>
                        Visitors. We Turn<br>
                        Them Into Buyers.
                    </h1>

                    <!-- Description -->
                    <p class="text-xl text-gray-200 leading-relaxed max-w-2xl">
                        Built for stores on Salla, Shopify, Zid, and WooCommerce. If you're spending on ads but not seeing sales — we'll fix the leaks and turn traffic into revenue.
                    </p>

                    <!-- CTA Button -->
                    <button class="bg-black/50 hover:bg-black/70 text-white px-8 py-4 rounded-lg font-semibold text-base transition-all transform hover:scale-105 inline-flex items-center space-x-3 backdrop-blur-sm border-2 border-white/20">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span>Watch Success Stories</span>
                    </button>

                    <!-- Star Rating -->
                    <div class="flex items-center space-x-4 pt-4">
                        <div class="flex text-yellow-400">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        </div>
                        <span class="text-gray-300">4.9/5 from 39+ reviews</span>
                    </div>
                </div>

                <!-- Right Side - Dashboard -->
                <div class="relative" data-aos="fade-left">
                    <div class="relative pt-20">
                        <!-- Floating Client Avatar - Positioned at top center -->
                        {{-- <div class="absolute -top-20 left-1/2 transform -translate-x-1/2 w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center border-4 border-black/30 backdrop-blur-sm z-10">
                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=80&h=80&fit=crop&crop=face&auto=format&q=60" alt="Client" class="w-20 h-20 rounded-full p-1">
                        </div> --}}
                        <div class="flex justify-center">
                            <div class="relative w-44 h-44 rounded-full overflow-hidden border-4 border-amber-500/30 shadow-2xl animate-fade-in mb-6">
                                <div class="relative overflow-hidden w-full h-full object-cover">
                                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=80&h=80&fit=crop&crop=face&auto=format&q=100" alt="Saudi Arabian e-commerce business owner working on laptop" loading="lazy" class="w-full h-full object-cover transition-all duration-500 opacity-100 scale-100">
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-b from-brand-blue/20 to-transparent">
                                </div>
                            </div>
                        </div>

                        <!-- Main Dashboard Card -->
                        <div class="bg-black/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 relative overflow-hidden mt-8 shadow-2xl">
                            <div class="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-amber-400 to-orange-500"></div>

                            <div class="text-center mb-6 pt-8">
                                <h3 class="text-white font-semibold mb-2 text-sm">Our Clients' Average Revenue Uplift in the first 4 months</h3>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="bg-black/30 backdrop-blur-sm rounded-xl p-4 text-center border border-white/10">
                                    <div class="text-3xl font-bold text-cyan-400 mb-1">157%</div>
                                    <p class="text-white text-xs font-medium">Average Revenue Increase</p>
                                    <p class="text-gray-400 text-xs">Across all clients in 2024</p>
                                </div>

                                <div class="bg-black/30 backdrop-blur-sm rounded-xl p-4 text-center border border-white/10">
                                    <div class="text-3xl font-bold text-cyan-400 mb-1">2.3x</div>
                                    <p class="text-white text-xs font-medium">Conversion Rate Improvement</p>
                                    <p class="text-gray-400 text-xs">From baseline to optimized</p>
                                </div>

                                <div class="bg-black/30 backdrop-blur-sm rounded-xl p-4 text-center border border-white/10">
                                    <div class="text-red-400 text-3xl font-bold mb-1">-35%</div>
                                    <p class="text-white text-xs font-medium">Cost Per Acquisition</p>
                                    <p class="text-gray-400 text-xs">Reduced marketing costs</p>
                                </div>

                                <div class="bg-black/30 backdrop-blur-sm rounded-xl p-4 text-center border border-white/10">
                                    <div class="text-3xl font-bold text-cyan-400 mb-1">47%</div>
                                    <p class="text-white text-xs font-medium">Add-to-Cart Rate</p>
                                    <p class="text-gray-400 text-xs">Improved product engagement</p>
                                </div>
                            </div>

                            <!-- Progress Bars -->
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between text-white text-xs mb-1">
                                        <span>Revenue Growth</span>
                                        <span class="text-amber-400 font-bold">+157%</span>
                                    </div>
                                    <div class="w-full bg-gray-700/50 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-amber-400 to-orange-500 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between text-white text-xs mb-1">
                                        <span>Conversion Rate</span>
                                        <span class="text-amber-400 font-bold">+230%</span>
                                    </div>
                                    <div class="w-full bg-gray-700/50 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-amber-400 to-orange-500 h-2 rounded-full" style="width: 92%"></div>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between text-white text-xs mb-1">
                                        <span>Client Satisfaction</span>
                                        <span class="text-amber-400 font-bold">4.8/5</span>
                                    </div>
                                    <div class="w-full bg-gray-700/50 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-amber-400 to-orange-500 h-2 rounded-full" style="width: 96%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Decorative Elements -->
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-amber-500/20 rounded-full blur-xl"></div>
                        <div class="absolute -top-4 -right-4 w-12 h-12 bg-orange-500/20 rounded-full blur-xl"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Results Showcase -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="text-primary-500 font-medium">We Don't Guess. We Deliver.</span>
                <h2 class="text-4xl font-space font-bold text-gray-900 mt-2 mb-4">
                    Real results from brands like yours in the Gulf region.
                </h2>
            </div>

            <!-- Mobile Results Carousel -->
            <div class="relative overflow-hidden">
                <div class="scrolling-wrapper flex space-x-8">
                    <!-- Mobile Mockups with Results -->
                    <div class="flex-shrink-0" data-aos="fade-up" data-aos-delay="100">
                        <img src="{{ asset('assets/mobile_1.png') }}" alt="Mobile Result 1" class="mobile-screen">
                    </div>

                    <div class="flex-shrink-0" data-aos="fade-up" data-aos-delay="200">
                        <img src="{{ asset('assets/mobile_2.png') }}" alt="Mobile Result 2" class="mobile-screen">
                    </div>

                    <div class="flex-shrink-0" data-aos="fade-up" data-aos-delay="300">
                        <img src="{{ asset('assets/mobile_3.png') }}" alt="Mobile Result 3" class="mobile-screen">
                    </div>

                    <div class="flex-shrink-0" data-aos="fade-up" data-aos-delay="400">
                        <img src="{{ asset('assets/mobile_4.png') }}" alt="Mobile Result 4" class="mobile-screen">
                    </div>

                    <!-- Duplicate for seamless scroll -->
                    <div class="flex-shrink-0">
                        <img src="{{ asset('assets/mobile_1.png') }}" alt="Mobile Result 1" class="mobile-screen">
                    </div>

                    <div class="flex-shrink-0">
                        <img src="{{ asset('assets/mobile_2.png') }}" alt="Mobile Result 2" class="mobile-screen">
                    </div>

                    <div class="flex-shrink-0">
                        <img src="{{ asset('assets/mobile_3.png') }}" alt="Mobile Result 3" class="mobile-screen">
                    </div>
                    
                    <div class="flex-shrink-0">
                        <img src="{{ asset('assets/mobile_4.png') }}" alt="Mobile Result 4" class="mobile-screen">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trusted Brands -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-500 text-sm mb-8 uppercase tracking-wider">Trusted by Leading GCC & MENA Brands</p>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
                <!-- Regal -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/3639185708.png" alt="Regal" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/ae.png" srcset="https://flagcdn.com/32x24/ae.png 2x, https://flagcdn.com/48x36/ae.png 3x" alt="UAE Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
                <!-- Ribal -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/1437761668.png" alt="Ribal" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/sa.png" srcset="https://flagcdn.com/32x24/sa.png 2x, https://flagcdn.com/48x36/sa.png 3x" alt="Saudi Arabia Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
                <!-- Succi -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/1353243521.png" alt="Succi" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/qa.png" srcset="https://flagcdn.com/32x24/qa.png 2x, https://flagcdn.com/48x36/qa.png 3x" alt="Qatar Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
                <!-- Vitrine -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/1810549676.png" alt="Vitrine" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/eg.png" srcset="https://flagcdn.com/32x24/eg.png 2x, https://flagcdn.com/48x36/eg.png 3x" alt="Egypt Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
                <!-- Dubai Phone -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/3310052489.png" alt="Dubai Phone" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/kw.png" srcset="https://flagcdn.com/32x24/kw.png 2x, https://flagcdn.com/48x36/kw.png 3x" alt="Kuwait Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
                <!-- Digital Force -->
                <div class="relative bg-white p-8 rounded-lg shadow-md h-32 flex items-center justify-center">
                    <img src="https://ext.same-assets.com/1428056230/400249477.png" alt="Digital Force" class="h-8 opacity-80">
                    <img src="https://flagcdn.com/16x12/us.png" srcset="https://flagcdn.com/32x24/us.png 2x, https://flagcdn.com/48x36/us.png 3x" alt="USA Flag" class="absolute top-0 right-0 h-8 w-8 rounded-full object-cover -mt-4 -mr-4 border-2 border-white">
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-space font-bold text-center text-gray-900 mb-16">Real Stories From Brands Like Yours</h2>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">G</div>
                        <div class="ml-4">
                            <h4 class="font-semibold text-gray-900">Ahmed Al-Mansouri</h4>
                            <p class="text-gray-600 text-sm">E-commerce Director</p>
                            <p class="text-gray-500 text-sm">Gulf Fashion Co.</p>
                        </div>
                    </div>
                    <blockquote class="text-gray-700 mb-4">"We used to guess. Now we know what works."</blockquote>
                    <div class="text-primary-600 font-semibold">Result: +47%</div>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold text-xl">T</div>
                        <div class="ml-4">
                            <h4 class="font-semibold text-gray-900">Sarah Al-Zahra</h4>
                            <p class="text-gray-600 text-sm">Marketing Manager</p>
                            <p class="text-gray-500 text-sm">Tech Solutions KW</p>
                        </div>
                    </div>
                    <blockquote class="text-gray-700 mb-4">"This is the first time someone actually ran experiments — not just gave tips."</blockquote>
                    <div class="text-primary-600 font-semibold">Result: +32%</div>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="200">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl">U</div>
                        <div class="ml-4">
                            <h4 class="font-semibold text-gray-900">Omar Hassan</h4>
                            <p class="text-gray-600 text-sm">Founder</p>
                            <p class="text-gray-500 text-sm">UAE Retail Group</p>
                        </div>
                    </div>
                    <blockquote class="text-gray-700 mb-4">"We used to guess. Now we know what works."</blockquote>
                    <div class="text-primary-600 font-semibold">Result: +28%</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why CRO Works -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="text-primary-500 font-medium">Why CRO Works</span>
                <h2 class="text-4xl font-space font-bold text-gray-900 mt-2">Best Practices Aren't a CRO Strategy.</h2>
            </div>

            <div class="max-w-4xl mx-auto">
                <p class="text-xl text-gray-600 text-center mb-12" data-aos="fade-up">
                    Changing a button color won't fix a broken funnel. Most brands rely on surface-level tips — but real CRO is a monthly system. We help you understand why users don't convert and run experiments to fix it — continuously.
                </p>

                <div class="bg-gray-50 rounded-2xl p-8" data-aos="fade-up">
                    <p class="text-center text-gray-700 mb-8">Small improvements in your conversion rate lead to massive revenue increases without additional ad spend.</p>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
                            <h3 class="font-semibold text-gray-900 mb-4">Before CRO</h3>
                            <div class="text-3xl font-bold text-red-600 mb-2">1.5%</div>
                            <p class="text-gray-600 mb-4">Current conversion rate</p>
                            <div class="text-2xl font-semibold text-gray-900">$75,000 revenue</div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-xl p-6 text-center">
                            <h3 class="font-semibold text-gray-900 mb-4">After CRO</h3>
                            <div class="text-3xl font-bold text-green-600 mb-2">3.0%</div>
                            <p class="text-gray-600 mb-4">Optimized conversion rate</p>
                            <div class="text-2xl font-semibold text-gray-900">$150,000 revenue</div>
                        </div>
                    </div>

                    <div class="text-center mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-xl">
                        <div class="text-3xl font-bold text-green-600 mb-2">+$75,000</div>
                        <p class="text-gray-700">Additional monthly revenue</p>
                        <p class="text-sm text-gray-600 mt-2">Without spending more on ads</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services -->
    <section id="services" class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span class="text-primary-500 font-medium">Our Services</span>
                <h2 class="text-4xl font-space font-bold text-gray-900 mt-2">Complete CRO Solutions for GCC E-Commerce</h2>
                <p class="text-xl text-gray-600 mt-4">Comprehensive conversion rate optimization tailored specifically for Middle Eastern platforms and Arabic customer behavior</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1" data-aos="fade-up">
                    <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Platform-Specific A/B Testing</h3>
                    <p class="text-gray-600 mb-6">Data-driven testing optimized for regional e-commerce platforms, Arabic checkout flows, and local payment methods (MADA, KNET).</p>
                    <ul class="space-y-2">
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Regional Store Optimization
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Payment Flow Testing
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            RTL Theme Support
                        </li>
                    </ul>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1" data-aos="fade-up" data-aos-delay="100">
                    <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Arabic UX/UI Optimization</h3>
                    <p class="text-gray-600 mb-6">Culturally-aware design improvements for Arabic customers, including RTL layouts, local payment preferences, and regional festivals.</p>
                    <ul class="space-y-2">
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            RTL Design Optimization
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Arabic Typography
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Cultural UX Elements
                        </li>
                    </ul>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                    <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">GCC Market Analytics</h3>
                    <p class="text-gray-600 mb-6">Deep-dive analytics for understanding Saudi, UAE, and Kuwait customer behavior patterns during Ramadan, Eid, and shopping seasons.</p>
                    <ul class="space-y-2">
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Regional Behavior Tracking
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Seasonal Performance Analysis
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Local Market Insights
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-dark-900 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-primary-600/20 to-blue-600/20"></div>
        <div class="container mx-auto px-6 text-center relative z-10">
            <div class="max-w-3xl mx-auto" data-aos="fade-up">
                <span class="inline-block bg-yellow-400 text-yellow-900 px-4 py-2 rounded-full text-sm font-medium mb-6">Free Strategy Session</span>
                <h2 class="text-4xl lg:text-5xl font-space font-bold text-white mb-6">
                    Let's Turn More Visitors Into Buyers
                </h2>
                <p class="text-xl text-gray-300 mb-8">
                    Book a free strategy session. We'll audit your store, show you where you're leaking revenue, and send you a conversion plan built just for your store.
                </p>
                <button class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 inline-flex items-center">
                    Book Session →
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-900 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">O</span>
                        </div>
                        <span class="font-space font-bold text-xl">Optimizers</span>
                    </div>
                    <p class="text-gray-400 mb-6">Leading CRO agency specializing in GCC e-commerce optimization. Boost your conversions without extra ad spend.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.222.083.343-.09.381-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.756-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/></svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold text-lg mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Case Studies</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">ROI Calculator</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-lg mb-6">Services</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Conversion Rate Optimization</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">A/B Testing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">User Experience Design</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Analytics & Tracking</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-lg mb-6">Contact Us</h3>
                    <div class="space-y-3">
                        <div class="flex items-center text-gray-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <div>
                                <p>Ras Al Khaimah Economic Zone Office</p>
                                <p class="text-sm">Ras Al Khaimah Economic Zone</p>
                            </div>
                        </div>
                        <div class="flex items-center text-gray-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span>+971 564800881</span>
                        </div>
                        <div class="flex items-center text-gray-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-8 mt-12 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">© 2024 Optimizers. All rights reserved.</p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Intersection Observer for Parallax -->
    <script src="https://cdn.jsdelivr.net/npm/intersection-observer@0.12.2/intersection-observer.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out-quart',
            once: true
        });

        // Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.count-up');

            counters.forEach(counter => {
                const target = parseFloat(counter.getAttribute('data-target'));
                const isDecimal = target % 1 !== 0;

                const countUp = new CountUp(counter, target, {
                    duration: 2,
                    decimal: isDecimal ? '.' : '',
                    decimalPlaces: isDecimal ? 1 : 0
                });

                if (!countUp.error) {
                    countUp.start();
                } else {
                    console.error(countUp.error);
                }
            });
        }

        // Parallax Effect
        function initParallax() {
            const parallaxElements = document.querySelectorAll('.parallax');

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    const yPos = -(scrollTop * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });
        }

        // Smooth scrolling for anchor links
        function initSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Intersection Observer for animations
        function initScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        if (entry.target.classList.contains('count-up')) {
                            // Trigger counter animation when visible
                            const target = parseFloat(entry.target.getAttribute('data-target'));
                            const isDecimal = target % 1 !== 0;

                            const countUp = new CountUp(entry.target, target, {
                                duration: 2,
                                decimal: isDecimal ? '.' : '',
                                decimalPlaces: isDecimal ? 1 : 0
                            });

                            if (!countUp.error) {
                                countUp.start();
                            }
                        }
                    }
                });
            });

            document.querySelectorAll('.count-up').forEach(el => {
                observer.observe(el);
            });
        }

        // Mobile menu toggle
        function initMobileMenu() {
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', () => {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        }

        // Loading animation
        function initLoadingAnimation() {
            window.addEventListener('load', () => {
                document.body.classList.add('loaded');
            });
        }

        // Initialize all functions
        document.addEventListener('DOMContentLoaded', function() {
            initSmoothScrolling();
            initScrollAnimations();
            initMobileMenu();
            initLoadingAnimation();

            // Add loading class removal after a delay
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 100);
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.classList.add('bg-dark-900');
                header.classList.remove('bg-dark-900/90');
            } else {
                header.classList.remove('bg-dark-900');
                header.classList.add('bg-dark-900/90');
            }
        });

        // Add hover effects for cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
