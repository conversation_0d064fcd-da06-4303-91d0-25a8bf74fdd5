<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Testimonial extends Model implements HasMedia
{
    use HasTranslations, InteractsWithMedia;

    protected $fillable = [
        'client_name',
        'client_title',
        'company_name',
        'content',
        'result_percentage',
        'video_url',
        'is_active',
        'sort_order'
    ];

    protected $translatable = [
        'client_name',
        'client_title',
        'company_name',
        'content'
    ];

    protected $casts = [
        'result_percentage' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(400)
            ->height(300)
            ->sharpen(10);

        $this->addMediaConversion('avatar')
            ->width(100)
            ->height(100)
            ->fit('crop', 100, 100);
    }
}
