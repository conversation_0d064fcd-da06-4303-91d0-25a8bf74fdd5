<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Testimonial;
use App\Models\Industry;
use App\Models\Service;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ContentController extends Controller
{
    /**
     * Get all brands with media
     */
    public function brands(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $brands = Brand::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($brand) use ($locale) {
                return [
                    'id' => $brand->id,
                    'name' => $brand->getTranslation('name', $locale),
                    'description' => $brand->getTranslation('description', $locale),
                    'country_code' => $brand->country_code,
                    'website_url' => $brand->website_url,
                    'logo' => $brand->getFirstMediaUrl('logos'),
                    'logo_thumb' => $brand->getFirstMediaUrl('logos', 'thumb'),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $brands,
            'locale' => $locale,
        ]);
    }

    /**
     * Get all testimonials with media
     */
    public function testimonials(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $testimonials = Testimonial::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($testimonial) use ($locale) {
                return [
                    'id' => $testimonial->id,
                    'client_name' => $testimonial->getTranslation('client_name', $locale),
                    'client_title' => $testimonial->getTranslation('client_title', $locale),
                    'company_name' => $testimonial->getTranslation('company_name', $locale),
                    'content' => $testimonial->getTranslation('content', $locale),
                    'result_percentage' => $testimonial->result_percentage,
                    'video_url' => $testimonial->video_url,
                    'avatar' => $testimonial->getFirstMediaUrl('avatars'),
                    'avatar_thumb' => $testimonial->getFirstMediaUrl('avatars', 'thumb'),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $testimonials,
            'locale' => $locale,
        ]);
    }

    /**
     * Get all industries
     */
    public function industries(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $industries = Industry::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($industry) use ($locale) {
                return [
                    'id' => $industry->id,
                    'name' => $industry->getTranslation('name', $locale),
                    'description' => $industry->getTranslation('description', $locale),
                    'icon_class' => $industry->icon_class,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $industries,
            'locale' => $locale,
        ]);
    }

    /**
     * Get all services
     */
    public function services(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        app()->setLocale($locale);

        $services = Service::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($service) use ($locale) {
                return [
                    'id' => $service->id,
                    'title' => $service->getTranslation('title', $locale),
                    'description' => $service->getTranslation('description', $locale),
                    'icon_class' => $service->icon_class,
                    'features' => $service->getTranslation('features', $locale),
                    'image' => $service->getFirstMediaUrl('images'),
                    'image_thumb' => $service->getFirstMediaUrl('images', 'thumb'),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $services,
            'locale' => $locale,
        ]);
    }

    /**
     * Get settings by group or all settings
     */
    public function settings(Request $request): JsonResponse
    {
        $locale = $request->get('locale', app()->getLocale());
        $group = $request->get('group');
        app()->setLocale($locale);

        $query = Setting::query();

        if ($group) {
            $query->where('group', $group);
        }

        $settings = $query->get()->mapWithKeys(function ($setting) use ($locale) {
            return [
                $setting->key => $setting->getTranslation('value', $locale)
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $settings,
            'locale' => $locale,
        ]);
    }
}
