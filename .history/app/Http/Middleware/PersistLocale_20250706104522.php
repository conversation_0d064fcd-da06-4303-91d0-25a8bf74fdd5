<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PersistLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from various sources in order of priority
        $locale = $this->getLocaleFromRequest($request);

        // Validate locale
        if (!in_array($locale, ['en', 'ar'])) {
            $locale = 'en'; // Default fallback
        }

        // Set application locale
        app()->setLocale($locale);

        // Store in session for persistence
        session(['locale' => $locale]);

        return $next($request);
    }

    /**
     * Get locale from request in order of priority
     */
    private function getLocaleFromRequest(Request $request): string
    {
        // 1. Check URL path (highest priority)
        $pathLocale = $this->getLocaleFromPath($request->path());
        if ($pathLocale) {
            return $pathLocale;
        }

        // 2. Check query parameter
        if ($request->has('locale')) {
            return $request->get('locale');
        }

        // 3. Check session
        if (session()->has('locale')) {
            return session('locale');
        }

        // 4. Check Accept-Language header
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage && str_contains($acceptLanguage, 'ar')) {
            return 'ar';
        }

        // 5. Default to English
        return 'en';
    }

    /**
     * Extract locale from URL path
     */
    private function getLocaleFromPath(string $path): ?string
    {
        if (preg_match('/^(en|ar)(?:\/|$)/', $path, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
