<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from request parameter, session, or default to 'en'
        $locale = $request->get('locale')
            ?? $request->header('Accept-Language')
            ?? session('locale')
            ?? 'en';

        // Validate locale
        if (!in_array($locale, ['en', 'ar'])) {
            $locale = 'en';
        }

        // Set application locale
        app()->setLocale($locale);

        // Store in session for future requests
        session(['locale' => $locale]);

        return $next($request);
    }
}
