<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Service;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'title' => [
                    'en' => 'Conversion Rate Optimization',
                    'ar' => 'تحسين معدل التحويل'
                ],
                'description' => [
                    'en' => 'Comprehensive CRO audits and optimization strategies to maximize your website\'s conversion potential.',
                    'ar' => 'مراجعات شاملة لتحسين معدل التحويل واستراتيجيات التحسين لتعظيم إمكانات التحويل لموقعك.'
                ],
                'features' => [
                    'en' => [
                        'A/B Testing & Multivariate Testing',
                        'User Experience (UX) Analysis',
                        'Landing Page Optimization',
                        'Checkout Process Optimization',
                        'Mobile Conversion Optimization'
                    ],
                    'ar' => [
                        'اختبار أ/ب والاختبار متعدد المتغيرات',
                        'تحليل تجربة المستخدم',
                        'تحسين صفحات الهبوط',
                        'تحسين عملية الدفع',
                        'تحسين التحويل للهاتف المحمول'
                    ]
                ],
                'icon_class' => 'chart-bar',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => [
                    'en' => 'Performance Analytics',
                    'ar' => 'تحليلات الأداء'
                ],
                'description' => [
                    'en' => 'Advanced analytics setup and reporting to track, measure, and optimize your conversion funnel.',
                    'ar' => 'إعداد التحليلات المتقدمة والتقارير لتتبع وقياس وتحسين قمع التحويل الخاص بك.'
                ],
                'features' => [
                    'en' => [
                        'Google Analytics 4 Setup',
                        'Conversion Tracking Implementation',
                        'Custom Dashboard Creation',
                        'ROI & Performance Reporting',
                        'Heat Mapping & User Recording'
                    ],
                    'ar' => [
                        'إعداد جوجل أناليتكس 4',
                        'تنفيذ تتبع التحويل',
                        'إنشاء لوحة تحكم مخصصة',
                        'تقارير العائد والأداء',
                        'خرائط الحرارة وتسجيل المستخدم'
                    ]
                ],
                'icon_class' => 'chart-pie',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => [
                    'en' => 'User Experience Optimization',
                    'ar' => 'تحسين تجربة المستخدم'
                ],
                'description' => [
                    'en' => 'Comprehensive UX audits and improvements to create seamless user journeys that convert.',
                    'ar' => 'مراجعات شاملة لتجربة المستخدم وتحسينات لإنشاء رحلات مستخدم سلسة تحقق التحويل.'
                ],
                'features' => [
                    'en' => [
                        'UX/UI Design Optimization',
                        'User Journey Mapping',
                        'Usability Testing',
                        'Mobile-First Design',
                        'Accessibility Improvements'
                    ],
                    'ar' => [
                        'تحسين تصميم تجربة/واجهة المستخدم',
                        'رسم خريطة رحلة المستخدم',
                        'اختبار سهولة الاستخدام',
                        'التصميم للهاتف المحمول أولاً',
                        'تحسينات إمكانية الوصول'
                    ]
                ],
                'icon_class' => 'user-group',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'title' => [
                    'en' => 'E-commerce Optimization',
                    'ar' => 'تحسين التجارة الإلكترونية'
                ],
                'description' => [
                    'en' => 'Specialized optimization for online stores to increase sales, reduce cart abandonment, and improve customer lifetime value.',
                    'ar' => 'تحسين متخصص للمتاجر الإلكترونية لزيادة المبيعات وتقليل هجر السلة وتحسين القيمة الدائمة للعميل.'
                ],
                'features' => [
                    'en' => [
                        'Product Page Optimization',
                        'Shopping Cart Optimization',
                        'Checkout Flow Improvement',
                        'Payment Gateway Optimization',
                        'Product Recommendation Engine'
                    ],
                    'ar' => [
                        'تحسين صفحات المنتجات',
                        'تحسين سلة التسوق',
                        'تحسين تدفق الدفع',
                        'تحسين بوابة الدفع',
                        'محرك توصيات المنتجات'
                    ]
                ],
                'icon_class' => 'shopping-bag',
                'is_active' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($services as $serviceData) {
            Service::create($serviceData);
        }
    }
}
