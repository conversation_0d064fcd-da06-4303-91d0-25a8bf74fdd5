<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Industry;

class IndustrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $industries = [
            [
                'name' => [
                    'en' => 'E-commerce & Retail',
                    'ar' => 'التجارة الإلكترونية والتجزئة'
                ],
                'description' => [
                    'en' => 'Optimize online stores and retail websites for maximum conversions and sales.',
                    'ar' => 'تحسين المتاجر الإلكترونية ومواقع التجزئة لتحقيق أقصى معدلات التحويل والمبيعات.'
                ],
                'icon_class' => 'shopping-cart',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => [
                    'en' => 'Financial Services',
                    'ar' => 'الخدمات المالية'
                ],
                'description' => [
                    'en' => 'Increase lead generation and customer acquisition for banks, insurance, and fintech.',
                    'ar' => 'زيادة توليد العملاء المحتملين واستقطاب العملاء للبنوك والتأمين والتكنولوجيا المالية.'
                ],
                'icon_class' => 'banknotes',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => [
                    'en' => 'Healthcare & Wellness',
                    'ar' => 'الرعاية الصحية والعافية'
                ],
                'description' => [
                    'en' => 'Improve patient acquisition and appointment bookings for healthcare providers.',
                    'ar' => 'تحسين استقطاب المرضى وحجز المواعيد لمقدمي الرعاية الصحية.'
                ],
                'icon_class' => 'heart',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => [
                    'en' => 'Real Estate',
                    'ar' => 'العقارات'
                ],
                'description' => [
                    'en' => 'Generate more qualified leads and property inquiries for real estate companies.',
                    'ar' => 'توليد المزيد من العملاء المؤهلين واستفسارات العقارات لشركات العقارات.'
                ],
                'icon_class' => 'building-office',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => [
                    'en' => 'Education & Training',
                    'ar' => 'التعليم والتدريب'
                ],
                'description' => [
                    'en' => 'Boost course enrollments and student registrations for educational institutions.',
                    'ar' => 'زيادة التسجيل في الدورات وتسجيل الطلاب للمؤسسات التعليمية.'
                ],
                'icon_class' => 'academic-cap',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => [
                    'en' => 'Travel & Hospitality',
                    'ar' => 'السفر والضيافة'
                ],
                'description' => [
                    'en' => 'Increase bookings and reservations for hotels, airlines, and travel agencies.',
                    'ar' => 'زيادة الحجوزات والتحفظات للفنادق وشركات الطيران ووكالات السفر.'
                ],
                'icon_class' => 'airplane',
                'is_active' => true,
                'sort_order' => 6
            ],
            [
                'name' => [
                    'en' => 'Technology & SaaS',
                    'ar' => 'التكنولوجيا والبرمجيات'
                ],
                'description' => [
                    'en' => 'Optimize trial signups and subscription conversions for tech companies.',
                    'ar' => 'تحسين التسجيل في النسخ التجريبية وتحويلات الاشتراك لشركات التكنولوجيا.'
                ],
                'icon_class' => 'computer-desktop',
                'is_active' => true,
                'sort_order' => 7
            ],
            [
                'name' => [
                    'en' => 'Automotive',
                    'ar' => 'السيارات'
                ],
                'description' => [
                    'en' => 'Drive more test drives and vehicle sales for automotive dealerships.',
                    'ar' => 'زيادة القيادة التجريبية ومبيعات المركبات لوكلاء السيارات.'
                ],
                'icon_class' => 'truck',
                'is_active' => true,
                'sort_order' => 8
            ]
        ];

        foreach ($industries as $industryData) {
            Industry::create($industryData);
        }
    }
}
