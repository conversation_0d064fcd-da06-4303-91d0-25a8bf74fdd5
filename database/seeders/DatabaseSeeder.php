<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user first
        $this->call(AdminUserSeeder::class);

        // Seed all content
        $this->call([
            SettingSeeder::class,
            PageSeeder::class,
            BrandSeeder::class,
            TestimonialSeeder::class,
            IndustrySeeder::class,
            ServiceSeeder::class,
        ]);

        $this->command->info('Database seeded successfully with comprehensive content!');
    }
}
